'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import ProductTabItemForm from '@/modules/product/components/add-product-tab-item';
import { IProductTabItem } from '@/types/product';
import ProductTabItemListAll from '@/modules/product/components/list-all-product-tab-item';

export default function ProductTabItemsPage() {
  const { id, productTabId } = useParams<{
    id: string;
    productTabId: string;
  }>();

  const [editingItem, setEditingItem] = useState<IProductTabItem | null>(null);

  return (
    <div className="p-6 container mx-auto min-h-screen">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Product Tab Items</h1>
        <div className="flex gap-4">
          <Link
            href={`/product/edit/${id}`}
            className="text-blue-600 hover:underline"
          >
            ← Back to Product
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-medium mb-4">Items in this Tab</h2>
            <ProductTabItemListAll/>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-medium mb-1">
              {editingItem ? 'Edit Item' : 'Create New Item'}
            </h2>
            <p className="text-sm text-muted-foreground mb-4">
              {editingItem ? 'Update the fields and save.' : 'Add a new item to this tab.'}
            </p>

            <ProductTabItemForm
              key={editingItem?.id ?? 'new'}
              productTabId={productTabId}
              productIdFilter={undefined}
              initialItem={editingItem ?? undefined}
              onClose={() => setEditingItem(null)}
              onSuccess={() => setEditingItem(null)}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
