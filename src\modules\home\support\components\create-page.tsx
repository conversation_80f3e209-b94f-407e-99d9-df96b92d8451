'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import ImageUpload from '@/components/image/image-upload';
import { useRouter } from 'next/navigation';

const SupportSectionCreatePage: React.FC = () => {
  const router = useRouter();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [title, setTitle] = useState('');
  const [subtitle, setSubtitle] = useState('');
  const [buttonLabel, setButtonLabel] = useState('');
  const [buttonUrl, setButtonUrl] = useState('');

  const handleCreate = () => {
    if (!imageUrl) {
      alert('Please upload an image');
      return;
    }
    if (!title.trim()) {
      alert('Title is required');
      return;
    }
    if (!subtitle.trim()) {
      alert('Subtitle is required');
      return;
    }
    if (!buttonLabel.trim()) {
      alert('Button label is required');
      return;
    }
    if (!buttonUrl.trim()) {
      alert('Button URL is required');
      return;
    }
    try {
      new URL(buttonUrl);
    } catch {
      alert('Button URL is invalid');
      return;
    }

    alert('Support section created (dummy)');
    router.push('/home');
    setImageUrl(null);
    setTitle('');
    setSubtitle('');
    setButtonLabel('');
    setButtonUrl('');
  };

  return (
    <div className="p-6 container mx-auto bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-6">Create Support Section</h2>

      {/* <ImageUpload
                value={imageUrl}
                onChange={setImageUrl}
                label="Support Image"
            /> */}

      <div className="mb-4">
        <label className="block mb-1 font-medium">Title</label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter title"
          className="w-full border rounded px-3 py-2"
        />
      </div>

      <div className="mb-4">
        <label className="block mb-1 font-medium">Subtitle</label>
        <input
          type="text"
          value={subtitle}
          onChange={(e) => setSubtitle(e.target.value)}
          placeholder="Enter subtitle"
          className="w-full border rounded px-3 py-2"
        />
      </div>

      <div className="mb-4">
        <label className="block mb-1 font-medium">Button Label</label>
        <input
          type="text"
          value={buttonLabel}
          onChange={(e) => setButtonLabel(e.target.value)}
          placeholder="Enter button label"
          className="w-full border rounded px-3 py-2"
        />
      </div>

      <div className="mb-4">
        <label className="block mb-1 font-medium">Button URL</label>
        <input
          type="url"
          value={buttonUrl}
          onChange={(e) => setButtonUrl(e.target.value)}
          placeholder="https://example.com"
          className="w-full border rounded px-3 py-2"
        />
      </div>
      <div className="flex gap-4">
        <Button
          onClick={handleCreate}
          className="mt-6 px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
        >
          Create
        </Button>
        <Button
          onClick={() => router.push('/home')}
          className="mt-6 px-6 py-2 bg-gray-400 hover:bg-gray-500 text-white rounded"
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default SupportSectionCreatePage;
