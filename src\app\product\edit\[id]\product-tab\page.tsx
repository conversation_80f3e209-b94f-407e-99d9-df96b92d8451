'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import ProductTabListByProductId from '@/modules/product/components/list-producttab';
import ProductTabForm from '@/modules/product/components/productTabSection';
import { IProductTab } from '@/types/product';

const ProductTabManagePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const productId = id;

  const [editingTab, setEditingTab] = useState<IProductTab | null>(null);

  return (
    <div className="p-6 container mx-auto min-h-screen">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Product Tabs</h1>
        <Link
          href={`/product/edit/${productId}`}
          className="text-blue-600 hover:underline"
        >
          ← Back to Product
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-medium mb-4">Existing Tabs</h2>
            <ProductTabListByProductId
              productId={productId}
              onEdit={(tab) => setEditingTab(tab)}
              onDone={() => setEditingTab(null)}
            />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <h2 className="text-lg font-medium mb-1">
              {editingTab ? 'Edit Tab' : 'Create a Tab'}
            </h2>
            <p className="text-sm text-muted-foreground mb-4">
              {editingTab ? 'Update the tab details and save.' : 'Create a new tab for this product.'}
            </p>

            <ProductTabForm
              key={editingTab?.id ?? 'new'}
              fixedProductId={productId}
              initialTab={editingTab ?? undefined}
              onClose={() => setEditingTab(null)}
              onSuccessRedirect={undefined}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProductTabManagePage;
