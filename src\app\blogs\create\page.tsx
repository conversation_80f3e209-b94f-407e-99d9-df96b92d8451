'use client';

import { useRouter } from 'next/navigation';
import { BlogForm } from '@/modules/blog/components/blog-form';
import { useCreateBlog } from '@/modules/blog/mutations/use-create-blog';

export default function CreateBlogPage() {
  const router = useRouter();
  const createBlog = useCreateBlog();

  const handleSubmit = async (data: any) => {
    try {
      await createBlog.mutateAsync(data);
      router.push('/blogs');
    } catch (error) {
      console.error('Failed to create blog:', error);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <BlogForm onSubmit={handleSubmit} loading={createBlog.isPending} />
    </div>
  );
}
