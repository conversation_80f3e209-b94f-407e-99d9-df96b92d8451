import { LoginCredentials, LoginResponse } from '@/types/user';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export const useLoginMutation = (
  onLoginSuccess: (response: LoginResponse) => void
) => {
  const queryClient = useQueryClient();

  return useMutation<LoginResponse, Error, LoginCredentials>({
    mutationFn: ({ email, password }: LoginCredentials) =>
      fetch(`/api/auth/login`, {
        mode: 'cors',
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: (response: LoginResponse) => {
      queryClient.invalidateQueries({ queryKey: ['user'] });
      toast.success('Logged in successfully');

      onLoginSuccess(response);
    },
    onError: (error) => {
      toast.error('Error logging in: ' + error.message);
    },
  });
};
