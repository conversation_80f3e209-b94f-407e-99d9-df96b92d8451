'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useCreateSubCategory } from '@/modules/subcategory/mutations/create-subcategory';
import { useGetCategory } from '@/modules/category/queries/list-category';
import { useRouter } from 'next/navigation';
import React, { useState, FormEvent, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

const SubcategoryCreatePage: React.FC = () => {
  const router = useRouter();
  const createSubCategoryMutation = useCreateSubCategory();
  const { data, isLoading, isError } = useGetCategory();

  const categories = data?.data ?? [];

  const [categoryId, setCategoryId] = useState<string | undefined>(undefined);
  const [name, setName] = useState('');
  const [slug, setSlug] = useState('');
  const [showOnNav, setShowOnNav] = useState(false);

  useEffect(() => {
    if (categories.length > 0 && !categoryId) {
      setCategoryId(categories[0].id);
    }
  }, [categories, categoryId]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();

    if (!categoryId) {
      alert('Please select a category');
      return;
    }

    createSubCategoryMutation.mutate(
      { id: '', name, slug, categoryId, showOnNav },
      {
        onSuccess: () => {
          router.push('/subcategory');
        },
        onError: () => {
          alert('Error creating subcategory');
        },
      }
    );
  };

  if (isLoading) {
    return (
      <div className="p-6 text-center text-gray-500">Loading categories...</div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 text-center text-red-600">
        Failed to load categories
      </div>
    );
  }

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow-md min-h-screen">
      <h1 className="text-2xl font-semibold mb-6">Create Subcategory</h1>
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label
            htmlFor="name"
            className="block mb-2 font-medium text-gray-700"
          >
            Name:
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            placeholder="Enter subcategory name"
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
          />
        </div>
        <div>
          <label
            htmlFor="slug"
            className="block mb-2 font-medium text-gray-700"
          >
            Slug:
          </label>
          <input
            id="slug"
            type="text"
            value={slug}
            onChange={(e) => setSlug(e.target.value)}
            required
            placeholder="Enter subcategory slug"
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
          />
        </div>
        <div>
          <label
            htmlFor="category"
            className="block mb-2 font-medium text-gray-700"
          >
            Category:
          </label>
          <Select value={categoryId} onValueChange={setCategoryId}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((cat) => (
                <SelectItem key={cat.id} value={cat.id}>
                  {cat.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="showOnNav"
              checked={showOnNav}
              onCheckedChange={(checked) => setShowOnNav(!!checked)}
            />
            <Label
              htmlFor="showOnNav"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Show on Nav
            </Label>
          </div>
        </div>

        <Button
          type="submit"
          className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition font-semibold"
        >
          Create
        </Button>
      </form>
    </div>
  );
};

export default SubcategoryCreatePage;
