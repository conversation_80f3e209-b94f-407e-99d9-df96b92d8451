'use client';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useRouter, useParams } from 'next/navigation';
import React, { useState, useEffect, FormEvent } from 'react';
import { useUpdateSubCategory } from '@/modules/subcategory/mutations/update-subcategory';
import { useGetSubCategory } from '@/modules/subcategory/queries/get-all-subcategory';
import { useGetCategory } from '@/modules/category/queries/list-category';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

const SubcategoryEditPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const slug = typeof params.slug === 'string' ? params.slug : '';

  const { data, isLoading, isError } = useGetSubCategory();
  const updateSubCategoryMutation = useUpdateSubCategory();

  const { data: categoryData } = useGetCategory();
  const initialCategories = categoryData?.data ?? [];

  const [name, setName] = useState('');
  const [newSlug, setNewSlug] = useState('');
  const [category, setCategory] = useState<string | null>(null);
  const [showOnNav, setShowOnNav] = useState(false);
  const [id, setId] = useState<string | null>(null);

  useEffect(() => {
    if (data?.data && slug) {
      const subcategory = data.data.find((sc) => sc.slug === slug);
      if (subcategory) {
        setName(subcategory.name);
        setNewSlug(subcategory.slug);
        setCategory(subcategory.categoryId);
        setId(subcategory.id);
        setShowOnNav(subcategory.showOnNav);
      }
    }
  }, [data, slug]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!id) return;

    updateSubCategoryMutation.mutate(
      { id, name, slug: newSlug, categoryId: category || '', showOnNav },
      {
        onSuccess: () => router.push('/subcategory'),
      }
    );
  };

  if (isLoading) return <div className="p-6">Loading subcategory...</div>;
  if (isError || !id)
    return <div className="p-6 text-red-600">Subcategory not found.</div>;

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow-md min-h-screen">
      <h1 className="text-2xl font-semibold mb-6">Edit Subcategory</h1>
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label
            htmlFor="name"
            className="block mb-2 font-medium text-gray-700"
          >
            Name:
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            placeholder="Enter subcategory name"
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
          />
        </div>
        <div>
          <label
            htmlFor="slug"
            className="block mb-2 font-medium text-gray-700"
          >
            Slug:
          </label>
          <input
            id="slug"
            type="text"
            value={newSlug}
            onChange={(e) => setNewSlug(e.target.value)}
            required
            placeholder="Enter subcategory slug"
            className="w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
          />
        </div>
        <div>
          <label
            htmlFor="category"
            className="block mb-2 font-medium text-gray-700"
          >
            Category:
          </label>
          <Select value={category || ''} onValueChange={setCategory}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              {initialCategories.map((cat) => (
                <SelectItem key={cat.id} value={cat.id}>
                  {cat.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="showOnNav"
              checked={showOnNav}
              onCheckedChange={(checked) => setShowOnNav(!!checked)}
            />
            <Label
              htmlFor="showOnNav"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Show on Nav
            </Label>
          </div>
        </div>

        <Button
          type="submit"
          className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 transition font-semibold"
        >
          Save
        </Button>
      </form>
    </div>
  );
};

export default SubcategoryEditPage;
